package alipay

import (
	"context"

	"github.com/go-pay/gopay"
	"github.com/go-pay/util"
	"github.com/go-pay/xlog"
)

func RefundOrder(outTradeNo, refundAmount string) (string, error) {
	ctx := context.Background()

	bm := make(gopay.BodyMap)
	bm.Set("out_trade_no", outTradeNo).
		Set("refund_amount", refundAmount).
		Set("out_request_no", util.RandomString(16)) // 每次退款请求唯一

	resp, err := Client.TradeRefund(ctx, bm)
	if err != nil {
		xlog.Errorf("TradeRefund err: %v", err)
		return "", err
	}

	return resp.Response.Msg, nil
}
