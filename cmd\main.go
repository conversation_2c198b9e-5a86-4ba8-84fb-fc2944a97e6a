package main

import (
	"alipay-demo/alipay"
	"fmt"
	"log"
	"net/http"
)

func main() {
	alipay - wrapper.InitClient()

	// curl -X GET http://127.0.0.1:8080/alipay/create_order
	http.HandleFunc("/alipay/create_order", func(w http.ResponseWriter, r *http.Request) {
		qr, err := alipay.CreateOrder()
		if err != nil {
			http.Error(w, "Create order failed", http.StatusInternalServerError)
			return
		}
		fmt.Fprintf(w, "QR Code: %s", qr)
	})

	// curl -X GET http://127.0.0.1:8080/alipay/query_order
	http.HandleFunc("/alipay/query_order", func(w http.ResponseWriter, r *http.Request) {
		resp, err := alipay.QueryOrder("20230903")
		if err != nil {
			http.Error(w, "Query failed", http.StatusInternalServerError)
			return
		}
		fmt.Fprintf(w, "TradeStatus: %s", resp.TradeStatus)
	})

	// curl -X GET http://127.0.0.1:8080/alipay/refund_order
	http.HandleFunc("/alipay/refund_order", func(w http.ResponseWriter, r *http.Request) {
		resp, err := alipay.RefundOrder("20230903", "0.01")
		if err != nil {
			http.Error(w, "Refund failed", http.StatusInternalServerError)
			return
		}
		fmt.Fprintf(w, "Refund Status: %s, Msg: %s", resp.Code, resp.Msg)
	})

	// curl -X POST http://127.0.0.1:8080/alipay/callback -d "out_trade_no=20230903&trade_status=TRADE_SUCCESS&total_amount=0.01&sign=xxxx"
	http.HandleFunc("/alipay/callback", alipay.HandleNotify)

	log.Println("Server running at http://127.0.0.1:8081")
	log.Fatal(http.ListenAndServe(":8081", nil))
}
