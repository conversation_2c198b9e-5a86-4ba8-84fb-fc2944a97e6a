package config

import (
	"os"
)

type Config struct {
	AppID               string
	PrivateKeyPath      string
	AlipayPublicKeyPath string
	NotifyURL           string
}

func LoadConfig() *Config {
	return &Config{
		AppID:               os.Getenv("APP_ID"),
		PrivateKeyPath:      os.<PERSON>en<PERSON>("PRIVATE_KEY_PATH"),
		AliPayPublicKeyPath: os.<PERSON>env("ALIPAY_PUBLIC_KEY_PATH"),
		NotifyURL:           os.<PERSON>env("NOTIFY_URL"),
	}
}
