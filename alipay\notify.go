package alipay

import (
	"log"
	"net/http"

	"github.com/go-pay/gopay/alipay/v3"
)

func HandleNotify(w http.ResponseWriter, r *http.Request) {
	notifyReq, err := alipay.ParseNotifyToBodyMap(r)
	if err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	ok, err := alipay.VerifySignWithPublicKey(notifyReq, AliPayPublicKey)
	if err != nil || !ok {
		log.Printf("Verify signature failed: %v", err)
		http.Error(w, "Invalid signature", http.StatusBadRequest)
		return
	}

	log.Printf("Payment notify received: %+v", notifyReq)
	w.Write([]byte("success"))
}
