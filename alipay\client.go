package alipay

import (
	"alipay-demo/config"
	"log"
	"os"

	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay/v3"
)

var Client *alipay.ClientV3
var AliPayPublicKey string

func newClient(c *config.Config) (*alipay.ClientV3, error) {
	client, err := alipay.NewClientV3(c.AppID, c.PrivateKeyPath, false)
	if err != nil {
		return nil, err
	}
	// 打开Debug开关，输出日志，默认关闭
	client.DebugSwitch = gopay.DebugOn

	return client, nil
}

func InitClient() {
	cfg := config.LoadConfig()
	var err error
	Client, err = newClient(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize Alipay client: %v", err)
	}

	// 读取支付宝公钥
	pubKeyBytes, err := os.ReadFile(cfg.AliPayPublicKeyPath)
	if err != nil {
		log.Fatalf("Failed to read Alipay public key: %v", err)
	}
	AliPayPublicKey = string(pubKeyBytes)
}
