package alipay

import (
	"context"

	"github.com/go-pay/gopay"
	"github.com/go-pay/util"
	"github.com/go-pay/xlog"
)

func CreateOrder() (string, error) {
	ctx := context.Background()

	bm := make(gopay.BodyMap)
	bm.Set("subject", "预创建订单").
		Set("out_trade_no", util.RandomString(32)).
		Set("total_amount", "0.01").
		Set("product_code", "FACE_TO_FACE_PAYMENT").
		Set("notify_url", "http://127.0.0.1:8080/alipay/callback")

	resp, err := Client.TradePrecreate(ctx, bm)
	if err != nil {
		xlog.Errorf("TradePrecreate err: %v", err)
		return "", err
	}

	xlog.Warnf("aliRsp: %s", gopay.JSON(resp))
	return resp.Response.QrCode, nil
}
